import { USER_ROLE } from '../constants.js';
import RoutePermissionManager from './RoutePermissionManager.js';
import ComponentPermissionManager from './ComponentPermissionManager.js';
import FeaturePermissionManager from './FeaturePermissionManager.js';
import RegionPermissionManager from './RegionPermissionManager.js';

/**
 * 主权限管理器单例
 * 统一管理所有子权限管理器，提供统一的权限检查接口
 */
class PermissionManager {
    constructor() {
        if (PermissionManager.instance) {
            return PermissionManager.instance;
        }

        // 延迟初始化其他权限管理器，只在构造函数中初始化区域权限管理器
        this.routeManager = null;
        this.componentManager = null;
        this.featureManager = null;
        this.regionManager = new RegionPermissionManager();

        this.initialized = false; // 整体初始化状态（用户登录后的权限）
        this.regionInitialized = false; // 区域权限初始化状态（应用初始化时的权限）
        this.userInfo = null;
        this.config = {};

        PermissionManager.instance = this;
    }

    /**
     * 获取单例实例
     * @returns {PermissionManager} 权限管理器实例
     */
    static getInstance() {
        if (!PermissionManager.instance) {
            PermissionManager.instance = new PermissionManager();
        }
        return PermissionManager.instance;
    }

    /**
     * 初始化区域权限管理器（在应用初始化时调用）
     * @param {Object} config - 配置信息
     */
    async initializeRegionPermissions(config = {}) {
        try {
            if (this.regionInitialized) {
                console.log('RegionPermissionManager already initialized');
                return;
            }

            this.config = { ...this.config, ...config };

            // 只初始化区域权限管理器
            await this.regionManager.initialize(null, this.config);
            this.regionInitialized = true;

            console.log('RegionPermissionManager initialized successfully');
        } catch (error) {
            console.error('Failed to initialize RegionPermissionManager:', error);
            throw error;
        }
    }

    /**
     * 初始化用户权限管理器（在用户登录后调用）
     * @param {Object} userInfo - 用户信息
     * @param {Object} config - 配置信息
     */
    async initialize(userInfo, config = {}) {
        try {
            this.userInfo = userInfo;
            this.config = { ...this.config, ...config };

            // 延迟创建其他权限管理器
            if (!this.routeManager) {
                this.routeManager = new RoutePermissionManager();
                this.componentManager = new ComponentPermissionManager();
                this.featureManager = new FeaturePermissionManager();

                // 设置路由管理器为全局实例
                RoutePermissionManager.setGlobalInstance(this.routeManager);
            }

            // 初始化用户相关的权限管理器
            await Promise.all([
                this.routeManager.initialize(userInfo, this.config),
                this.componentManager.initialize(userInfo, this.config),
                this.featureManager.initialize(userInfo, this.config)
            ]);

            this.initialized = true;

            // 触发初始化完成事件
            this.emitEvent('initialized', { userInfo, config: this.config });

            console.log('PermissionManager initialized successfully', userInfo);
        } catch (error) {
            console.error('Failed to initialize PermissionManager:', error);
            throw error;
        }
    }

    /**
     * 检查路由权限
     * @param {string} routePath - 路由路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkRoutePermission(routePath, context = {}) {
        if (!this.initialized || !this.routeManager) {
            console.warn('RoutePermissionManager not initialized, returning false for route:', routePath);
            return false;
        }
        return this.routeManager.hasPermission(routePath, context);
    }

    /**
     * 检查组件权限
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkComponentPermission(component, action = null, context = {}) {
        if (!this.initialized || !this.componentManager) {
            console.warn('ComponentPermissionManager not initialized, returning false for component:', component);
            return false;
        }
        return this.componentManager.hasPermission(component, action, context);
    }

    /**
     * 检查功能权限
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkFeaturePermission(feature, action = null, context = {}) {
        if (!this.initialized || !this.featureManager) {
            console.warn('FeaturePermissionManager not initialized, returning false for feature:', feature);
            return false;
        }
        return this.featureManager.hasPermission(feature, action, context);
    }

    /**
     * 检查区域功能权限
     * @param {string} functionName - 功能名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkRegionPermission(functionName, context = {}) {
        if (!this.regionInitialized) {
            console.warn('RegionPermissionManager not initialized, returning false for region function:', functionName);
            return false;
        }
        return this.regionManager.hasPermission(functionName, context);
    }

    /**
     * 通用权限检查方法
     * 根据权限标识自动判断权限类型并进行检查
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkPermission(permission, context = {}) {
        // 如果未初始化，返回false而不是抛出错误
        if (!this.initialized) {
            console.warn('PermissionManager not initialized, returning false for permission:', permission);
            return false;
        }
        // 检查是否为区域功能权限（包括直接的区域功能和映射的权限）
        if (this.regionInitialized) {
            // 先检查是否是直接的区域功能
            const regionFunctions = [
                'live', 'library', 'cloudStatistic', 'breastCases', 'breastAI',
                'drAIAssistant', 'groupset', 'wechat', 'obstetricalAI', 'tvwall',
                'qcStatistics', 'referralCode', 'webShareScreen', 'webTvwallEnterConversation',
                'ai', 'smartEdTechTraining', 'ultrasoundQCReport', 'club', 'professionalIdentityForce'
            ];
            if (regionFunctions.includes(permission)) {
                return this.checkRegionPermission(permission, context);
            }

            // 检查是否是映射的权限
            const mappedRegionFunction = this.regionManager.findRegionFunctionByMappedPermission(permission);
            if (mappedRegionFunction) {
                return this.checkRegionPermission(permission, context);
            }
        }
        // 如果不在预定义权限中，尝试作为功能权限检查
        return this.checkFeaturePermission(permission, null, context);
    }

    /**
     * 检查API权限
     * @param {string} method - HTTP方法
     * @param {string} path - API路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkApiPermission(method, path, context = {}) {
        this.ensureInitialized();
        return this.featureManager.checkApiPermission(method, path, context);
    }

    /**
     * 检查数据权限
     * @param {string} dataType - 数据类型
     * @param {string} scope - 数据范围
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkDataPermission(dataType, scope, context = {}) {
        this.ensureInitialized();
        return this.featureManager.checkDataPermission(dataType, scope, context);
    }

    /**
     * 检查元素权限
     * @param {string} elementSelector - 元素选择器
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkElementPermission(elementSelector, context = {}) {
        this.ensureInitialized();
        return this.componentManager.checkElementPermission(elementSelector, context);
    }

    /**
     * 获取组件可见性
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否可见
     */
    isComponentVisible(component, action = null, context = {}) {
        return this.checkComponentPermission(component, action, context);
    }

    /**
     * 获取组件禁用状态
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否禁用
     */
    isComponentDisabled(component, action = null, context = {}) {
        return !this.checkComponentPermission(component, action, context);
    }

    /**
     * 获取用户可访问的路由列表
     * @returns {Array<string>} 可访问的路由列表
     */
    getAccessibleRoutes() {
        this.ensureInitialized();
        return this.routeManager.getAccessibleRoutes();
    }

    /**
     * 获取用户可执行的功能操作
     * @param {string} feature - 功能名称
     * @returns {Array<string>} 可执行的操作列表
     */
    getAvailableActions(feature) {
        this.ensureInitialized();
        return this.featureManager.getAvailableActions(feature);
    }

    /**
     * 获取重定向路由
     * @param {string} originalRoute - 原始路由
     * @returns {string} 重定向路由
     */
    getRedirectRoute(originalRoute) {
        this.ensureInitialized();
        return this.routeManager.getRedirectRoute(originalRoute);
    }

    /**
     * 批量检查权限
     * @param {Object} permissions - 权限检查配置
     * @returns {Object} 权限检查结果
     */
    batchCheckPermissions(permissions) {
        this.ensureInitialized();
        const results = {};

        // 检查路由权限
        if (permissions.routes) {
            permissions.routes.forEach(({ route, context = {}, key }) => {
                const permissionKey = key || `route_${route.replace(/\//g, '_')}`;
                results[permissionKey] = this.checkRoutePermission(route, context);
            });
        }

        // 检查组件权限
        if (permissions.components) {
            const componentResults = this.componentManager.batchCheckPermissions(permissions.components);
            Object.assign(results, componentResults);
        }

        // 检查功能权限
        if (permissions.features) {
            const featureResults = this.featureManager.batchCheckPermissions(permissions.features);
            Object.assign(results, featureResults);
        }

        return results;
    }

    /**
     * 更新用户信息
     * @param {Object} userInfo - 新的用户信息
     */
    updateUserInfo(userInfo) {
        this.userInfo = { ...this.userInfo, ...userInfo };

        // 更新所有已初始化的子管理器的用户信息
        if (this.routeManager) {
            this.routeManager.updateUserInfo(this.userInfo);
        }
        if (this.componentManager) {
            this.componentManager.updateUserInfo(this.userInfo);
        }
        if (this.featureManager) {
            this.featureManager.updateUserInfo(this.userInfo);
        }
        if (this.regionManager) {
            this.regionManager.updateUserInfo(this.userInfo);
        }

        // 触发用户信息更新事件
        this.emitEvent('userInfoUpdated', this.userInfo);
    }

    /**
     * 清除所有权限缓存
     */
    clearCache() {
        if (this.routeManager) {
            this.routeManager.clearCache();
        }
        if (this.componentManager) {
            this.componentManager.clearCache();
        }
        if (this.featureManager) {
            this.featureManager.clearCache();
        }
        if (this.regionManager) {
            this.regionManager.clearCache();
        }
    }

    /**
     * 获取当前用户信息
     * @returns {Object} 用户信息
     */
    getUserInfo() {
        return this.userInfo;
    }

    /**
     * 获取用户角色
     * @returns {number|string} 用户角色
     */
    getUserRole() {
        return this.userInfo?.role || 0;
    }

    /**
     * 获取用户ID
     * @returns {string|number} 用户ID
     */
    getUserId() {
        return this.userInfo?.uid || this.userInfo?.id;
    }

    /**
     * 检查是否为管理员
     * @returns {boolean} 是否为管理员
     */
    isAdmin() {
        if (!this.initialized) {
            return false;
        }
        const role = this.getUserRole();
        return role === USER_ROLE.ADMIN || role === USER_ROLE.SUPER_ADMIN;
    }

    /**
     * 检查是否为主任
     * @returns {boolean} 是否为主任
     */
    isDirector() {
        if (!this.initialized) {
            return false;
        }
        const role = this.getUserRole();
        return role === USER_ROLE.DIRECTOR;
    }

    /**
     * 检查是否为超级管理员
     * @returns {boolean} 是否为超级管理员
     */
    isSuperAdmin() {
        if (!this.initialized) {
            return false;
        }
        return this.getUserRole() === USER_ROLE.SUPER_ADMIN; // 超级管理员
    }

    /**
     * 检查是否已初始化
     * @returns {boolean} 是否已初始化
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * 检查区域功能是否启用
     * @param {string} functionName - 功能名称
     * @returns {boolean} 是否启用
     */
    isRegionFunctionEnabled(functionName) {
        if (!this.regionInitialized) {
            return false;
        }
        return this.regionManager.isFunctionEnabled(functionName);
    }

    /**
     * 获取所有启用的区域功能
     * @returns {Array<string>} 启用的功能列表
     */
    getEnabledRegionFunctions() {
        if (!this.regionInitialized) {
            return [];
        }
        return this.regionManager.getEnabledFunctions();
    }

    /**
     * 获取当前区域
     * @returns {string} 当前区域
     */
    getCurrentRegion() {
        if (!this.regionInitialized) {
            return '';
        }
        return this.regionManager.getCurrentRegion();
    }

    /**
     * 检查功能在当前区域是否可用
     * @param {string} functionName - 功能名称
     * @param {Object} options - 选项
     * @returns {boolean} 是否可用
     */
    isRegionFunctionAvailable(functionName, options = {}) {
        if (!this.regionInitialized) {
            return false;
        }
        return this.regionManager.isFunctionAvailable(functionName, options);
    }

    /**
     * 获取指定区域功能的所有映射权限
     * @param {string} regionFunction - 区域功能名称
     * @returns {Array<string>} 映射的权限列表
     */
    getRegionMappedPermissions(regionFunction) {
        if (!this.regionInitialized) {
            return [];
        }
        return this.regionManager.getMappedPermissions(regionFunction);
    }

    /**
     * 检查指定区域功能的所有映射权限
     * @param {string} regionFunction - 区域功能名称
     * @returns {Object} 映射权限的检查结果
     */
    checkAllRegionMappedPermissions(regionFunction) {
        if (!this.regionInitialized) {
            return {};
        }
        return this.regionManager.checkAllMappedPermissions(regionFunction);
    }

    /**
     * 获取所有启用的映射权限
     * @returns {Array<string>} 所有启用的映射权限列表
     */
    getAllEnabledMappedPermissions() {
        if (!this.regionInitialized) {
            return [];
        }
        return this.regionManager.getAllEnabledMappedPermissions();
    }

    /**
     * 获取权限映射关系摘要
     * @returns {Object} 权限映射摘要
     */
    getRegionPermissionMappingSummary() {
        if (!this.regionInitialized) {
            return {};
        }
        return this.regionManager.getPermissionMappingSummary();
    }

    /**
     * 确保已初始化
     */
    ensureInitialized() {
        if (!this.initialized) {
            throw new Error('PermissionManager not initialized. Please call initialize() first.');
        }
    }

    /**
     * 销毁权限管理器
     */
    destroy() {
        if (this.routeManager) {
            this.routeManager.destroy();
            this.routeManager = null;
        }
        if (this.componentManager) {
            this.componentManager.destroy();
            this.componentManager = null;
        }
        if (this.featureManager) {
            this.featureManager.destroy();
            this.featureManager = null;
        }
        if (this.regionManager) {
            this.regionManager.destroy();
            this.regionManager = null;
        }

        this.initialized = false;
        this.regionInitialized = false;
        this.userInfo = null;
        this.config = {};

        // 清除单例实例
        PermissionManager.instance = null;

        // 触发销毁事件
        this.emitEvent('destroyed');
    }

    /**
     * 触发事件
     * @param {string} eventName - 事件名称
     * @param {any} data - 事件数据
     */
    emitEvent(eventName, data = null) {
        if (window.vm && window.vm.$emit) {
            window.vm.$emit(`permission:${eventName}`, data);
        }

        // 也可以使用自定义事件
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const event = new CustomEvent(`permission:${eventName}`, { detail: data });
            window.dispatchEvent(event);
        }
    }

    /**
     * 获取子管理器
     * @param {string} type - 管理器类型 (route|component|feature|region)
     * @returns {Object} 子管理器实例
     */
    getManager(type) {
        switch (type) {
        case 'route':
            if (!this.routeManager) {
                console.warn('RoutePermissionManager not initialized');
            }
            return this.routeManager;
        case 'component':
            if (!this.componentManager) {
                console.warn('ComponentPermissionManager not initialized');
            }
            return this.componentManager;
        case 'feature':
            if (!this.featureManager) {
                console.warn('FeaturePermissionManager not initialized');
            }
            return this.featureManager;
        case 'region':
            if (!this.regionManager) {
                console.warn('RegionPermissionManager not initialized');
            }
            return this.regionManager;
        default:
            throw new Error(`Unknown manager type: ${type}`);
        }
    }
}

// 创建全局实例
const permissionManager = new PermissionManager();

export default permissionManager;
export { PermissionManager };
